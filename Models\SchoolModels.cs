using System.ComponentModel.DataAnnotations;

namespace CareerGuide.Models
{
    // School Model
    public class School
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // PUBLIC, PRIVATE
        
        [Required]
        [StringLength(200)]
        public string Location { get; set; } = string.Empty;
        
        [Range(0, 100)]
        public decimal Score { get; set; }
        
        [StringLength(500)]
        public string ImageUrl { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string TuitionRange { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Website { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;
        
        public DateTime EstablishedYear { get; set; }
        
        public int StudentCount { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public virtual ICollection<Major> Majors { get; set; } = new List<Major>();
        public virtual ICollection<SchoolRecommendation> SchoolRecommendations { get; set; } = new List<SchoolRecommendation>();
        public virtual ICollection<UserFavoriteSchool> UserFavorites { get; set; } = new List<UserFavoriteSchool>();
    }

    // Major Model
    public class Major
    {
        public int Id { get; set; }
        
        public int SchoolId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string Duration { get; set; } = string.Empty; // 4 năm, 3.5 năm
        
        [StringLength(200)]
        public string TuitionFee { get; set; } = string.Empty;
        
        public int AdmissionScore { get; set; }
        
        [StringLength(500)]
        public string CareerProspects { get; set; } = string.Empty;
        
        public bool IsPopular { get; set; } = false;
        
        public virtual School School { get; set; } = null!;
    }

    // School Recommendation Model
    public class SchoolRecommendation
    {
        public int Id { get; set; }
        
        public int PersonalityTypeId { get; set; }
        
        public int SchoolId { get; set; }
        
        public int RecommendationScore { get; set; } // 0-100
        
        [StringLength(500)]
        public string Reason { get; set; } = string.Empty;
        
        public virtual PersonalityType PersonalityType { get; set; } = null!;
        public virtual School School { get; set; } = null!;
    }

    // User Favorite School Model
    public class UserFavoriteSchool
    {
        public int Id { get; set; }
        
        public string SessionId { get; set; } = string.Empty;
        
        public int SchoolId { get; set; }
        
        public DateTime AddedAt { get; set; } = DateTime.UtcNow;
        
        public virtual School School { get; set; } = null!;
    }

    // School Ranking Model
    public class SchoolRanking
    {
        public int Id { get; set; }
        
        public int SchoolId { get; set; }
        
        public int Year { get; set; }
        
        public int NationalRank { get; set; }
        
        public int RegionalRank { get; set; }
        
        [StringLength(100)]
        public string RankingOrganization { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Criteria { get; set; } = string.Empty;
        
        public virtual School School { get; set; } = null!;
    }

    // School Statistics Model
    public class SchoolStatistics
    {
        public int Id { get; set; }
        
        public int SchoolId { get; set; }
        
        public int Year { get; set; }
        
        public int TotalStudents { get; set; }
        
        public int NewStudents { get; set; }
        
        public int Graduates { get; set; }
        
        public decimal EmploymentRate { get; set; } // Tỷ lệ có việc làm
        
        public decimal AverageSalary { get; set; }
        
        public int TotalFaculty { get; set; }
        
        public decimal StudentFacultyRatio { get; set; }
        
        public virtual School School { get; set; } = null!;
    }
}
