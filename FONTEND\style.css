/* ===== GLOBAL STYLES ===== */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #2d3748;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    overflow-x: hidden;
}

* {
    box-sizing: border-box;
}

/* ===== HEADER STYLES ===== */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 30px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    border-radius: 20px;
    margin-bottom: 30px;
    margin-top: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.header img {
    width: 60px;
    height: auto;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.header img:hover {
    transform: scale(1.1);
}

.header .logo {
    font-size: 2rem;
    font-weight: 800;
    color: #ffffff;
    text-transform: uppercase;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== HERO SECTION ===== */
.hero-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%),
                url(./image/bia.png) no-repeat center center/cover;
    color: white;
    padding: 120px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    border-radius: 25px;
    margin: 20px 0;
}

.hero-text {
    font-size: 4.5rem;
    font-weight: 900;
    line-height: 1.1;
    text-transform: uppercase;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-bubbles {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 50%;
    z-index: 1;
}
/* ===== BUTTON STYLES ===== */
.header .btn-login,
.header .btn-signup {
    background-color: transparent;
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: #ffffff;
    padding: 12px 24px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
    margin-left: 10px;
    backdrop-filter: blur(10px);
}

.header .btn-login:hover,
.header .btn-signup:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

.btn-outline-primary {
    border: 2px solid #667eea;
    color: #667eea;
    padding: 12px 24px;
    border-radius: 50px;
    transition: all 0.3s ease;
    font-weight: 600;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    transform: translateY(-2px);
    border-color: transparent;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}
/* ===== FEATURES SECTION ===== */
.features-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 80px 0;
    text-align: center;
    position: relative;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.feature-card {
    text-align: center;
    padding: 40px 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
}

.feature-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    background: rgba(255, 255, 255, 0.95);
}

.feature-icon {
    font-size: 3.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 25px;
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-title {
    font-weight: 700;
    margin-bottom: 15px;
    color: #2d3748;
    font-size: 1.3rem;
}

.feature-text {
    color: #718096;
    font-size: 1rem;
    line-height: 1.6;
}
/* ===== BENEFITS SECTION ===== */
.benefits-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.benefits-title {
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 40px;
    font-size: 2.5rem;
    line-height: 1.2;
}

.benefit-item {
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    color: #4a5568;
    padding: 15px 0;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    transform: translateX(10px);
    color: #2d3748;
}

.benefit-check {
    color: #667eea;
    margin-right: 15px;
    font-size: 1.2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.benefits-section img {
    width: 100%;
    max-width: 500px;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 25px;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    transition: transform 0.3s ease;
}

.benefits-section img:hover {
    transform: scale(1.05);
}
/* ===== FOOTER SECTION ===== */
.footer-section {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
}

.footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23footerPattern)"/></svg>');
}

.footer-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.footer-subtitle {
    font-size: 2rem;
    margin-bottom: 25px;
    color: #e2e8f0;
    position: relative;
    z-index: 1;
}

.footer-text {
    font-size: 1.1rem;
    margin-bottom: 20px;
    line-height: 1.7;
    color: #cbd5e0;
    position: relative;
    z-index: 1;
}

.footer-section img {
    width: 100%;
    max-width: 450px;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    position: relative;
    z-index: 1;
}

.footer-section img:hover {
    transform: scale(1.05);
}
/* ===== CONTACT BAR ===== */
.contact-bar {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50px;
    padding: 20px 40px;
    margin-top: 40px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.contact-bar:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
}

.contact-item {
    color: #2d3748;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 10px 15px;
    border-radius: 25px;
    display: flex;
    align-items: center;
}

.contact-item:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.05);
}

/* ===== UTILITY CLASSES ===== */
.position-relative {
    z-index: 2;
}

.logo-large {
    font-size: 2.8rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.img-fluid {
    border-radius: 20px;
    max-width: 100%;
    height: auto;
    transition: transform 0.3s ease;
}

.img-fluid:hover {
    transform: scale(1.02);
}
/* ===== MBTI ASSESSMENT STYLES ===== */
.btn-get-started {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-get-started:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    transform: translateY(-3px);
}

.user-icon {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #667eea;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.user-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.hero-section {
    position: relative;
    overflow: hidden;
    border-radius: 25px;
    margin: 20px 0;
}

.hero-image {
    width: 100%;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 25px;
    transition: transform 0.3s ease;
}

.hero-image:hover {
    transform: scale(1.02);
}
.assessment-header {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    padding: 50px 20px;
    text-align: center;
    border-radius: 25px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.assessment-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="assessmentPattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23667eea" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23assessmentPattern)"/></svg>');
}

.assessment-title {
    font-size: 2.8rem;
    font-weight: 900;
    text-transform: uppercase;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.assessment-subtitle {
    font-size: 1.6rem;
    font-weight: 500;
    text-transform: uppercase;
    color: #e2e8f0;
    position: relative;
    z-index: 1;
}

.progress-indicator {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #2d3748;
    font-weight: 700;
    font-size: 1.3rem;
    padding: 12px 35px;
    border-radius: 50px;
    display: inline-block;
    margin-top: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.progress-indicator:hover {
    transform: scale(1.05);
}
.career-path-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 80px 0;
    position: relative;
}

.career-path-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="careerPattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23careerPattern)"/></svg>');
}

.career-path-section img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 25px;
    margin: 0 auto;
    display: block;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    transition: transform 0.3s ease;
    position: relative;
    z-index: 1;
}

.career-path-section img:hover {
    transform: scale(1.05);
}

.career-path-title {
    font-size: 3.2rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.career-path-text {
    font-size: 1.3rem;
    color: #4a5568;
    margin-bottom: 35px;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.btn-take-assessment {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    border-radius: 50px;
    padding: 15px 35px;
    font-weight: 700;
    border: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(45, 55, 72, 0.3);
    position: relative;
    z-index: 1;
}

.btn-take-assessment:hover {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(45, 55, 72, 0.4);
}
.question-section {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    padding: 80px 20px;
    text-align: center;
    border-radius: 25px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
}

.question-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="questionPattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1.5" fill="%23667eea" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23questionPattern)"/></svg>');
}

.question-section img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 20px;
    margin: 0 auto;
    display: block;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.question-text {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 50px;
    line-height: 1.3;
    position: relative;
    z-index: 1;
    color: #e2e8f0;
}

.answer-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 15px 35px;
    font-size: 1.2rem;
    margin: 15px;
    width: 100%;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.answer-btn:hover {
    background: rgba(102, 126, 234, 0.3);
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.answer-btn:active,
.answer-btn.selected {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    transform: scale(0.98);
}

.btn-confirm {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #2d3748;
    border-radius: 50px;
    padding: 15px 50px;
    font-weight: 700;
    font-size: 1.2rem;
    margin-top: 40px;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.btn-confirm:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
/* ===== SCHOOL RECOMMENDATIONS STYLES ===== */
.recommendations-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 60px 0;
    position: relative;
}

.recommendations-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="recommendationPattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1.5" fill="%23667eea" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23recommendationPattern)"/></svg>');
}

.recommendations-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.recommendations-text {
    font-size: 1.2rem;
    color: #4a5568;
    margin-bottom: 35px;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.btn-filters {
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 50px;
    padding: 12px 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.btn-filters:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.btn-filters i {
    margin-right: 10px;
    color: #667eea;
}
.school-list {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    padding: 40px 0;
    position: relative;
}

.school-list::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="schoolPattern" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="%23667eea" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23schoolPattern)"/></svg>');
}

.school-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    z-index: 1;
}

.school-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 1);
}

.school-image {
    width: 100%;
    max-width: 150px;
    height: auto;
    border-radius: 15px;
    margin-right: 25px;
    transition: transform 0.3s ease;
}

.school-card:hover .school-image {
    transform: scale(1.05);
}

.school-name {
    font-size: 1.6rem;
    font-weight: 800;
    margin-bottom: 8px;
    color: #2d3748;
}

.school-type {
    font-size: 1rem;
    color: #718096;
    text-transform: uppercase;
    margin-bottom: 15px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.school-score {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.1rem;
    position: absolute;
    left: 160px;
    bottom: 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.school-card:hover .school-score {
    transform: scale(1.1);
}

.btn-see {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 35px;
    font-weight: 700;
    position: absolute;
    right: 25px;
    bottom: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-see:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}
.arrow-marker {
    position: absolute;
    left: -25px;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    font-size: 1.8rem;
    background: rgba(255, 255, 255, 0.9);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.school-card:hover .arrow-marker {
    transform: translateY(-50%) scale(1.1);
    background: #667eea;
    color: white;
}

.btn-more {
    background: rgba(255, 255, 255, 0.9);
    color: #2d3748;
    border-radius: 50px;
    padding: 12px 40px;
    font-weight: 700;
    margin: 30px auto;
    display: block;
    border: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.btn-more:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(255, 255, 255, 0.3);
}

.top-schools-section {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    color: white;
    padding: 60px 0;
    position: relative;
}

.top-schools-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="topSchoolsPattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="%23667eea" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23topSchoolsPattern)"/></svg>');
}

.top-schools-title {
    font-size: 2.2rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 1;
}

.school-ranking-table {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.school-ranking-row {
    display: flex;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.school-ranking-row:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.school-ranking-row:last-child {
    border-bottom: none;
}

.school-ranking-name {
    flex: 1;
    padding: 20px;
    text-align: right;
    font-weight: 600;
    color: #2d3748;
    font-size: 1.1rem;
}

.school-ranking-score {
    width: 40%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    text-align: right;
    font-weight: 800;
    color: white;
    font-size: 1.2rem;
    position: relative;
}

.school-ranking-score::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: rgba(255, 255, 255, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-text {
        font-size: 3rem;
    }

    .feature-card {
        margin-bottom: 20px;
    }

    .school-card {
        flex-direction: column;
        text-align: center;
    }

    .school-image {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .school-score {
        position: static;
        margin: 10px auto;
    }

    .btn-see {
        position: static;
        margin-top: 15px;
    }

    .arrow-marker {
        display: none;
    }
}
