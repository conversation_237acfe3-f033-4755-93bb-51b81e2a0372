/**
 * CAREER GUIDE - JavaScript Events & Functions
 * Trang web tư vấn trường đại học cho học sinh cấp ba
 * Author: Career Guide Team
 */

// ===== GLOBAL VARIABLES =====
let currentQuestion = 0;
let totalQuestions = 20;
let userAnswers = [];
let mbtiScores = {
    E: 0, I: 0,  // Extraversion vs Introversion
    S: 0, N: 0,  // Sensing vs Intuition
    T: 0, F: 0,  // Thinking vs Feeling
    J: 0, P: 0   // Judging vs Perceiving
};

// ===== MBTI QUESTIONS DATABASE =====
const mbtiQuestions = [
    {
        id: 1,
        question: "Bạn có thấy dễ dàng giới thiệu bản thân với người khác không?",
        answers: [
            { text: "Rất đồng ý", type: "E", value: 3 },
            { text: "Đồng ý", type: "E", value: 2 },
            { text: "Không đồng ý", type: "I", value: 2 },
            { text: "Rất không đồng ý", type: "I", value: 3 }
        ]
    },
    {
        id: 2,
        question: "<PERSON>ạn thích làm việc với các chi tiết cụ thể hơn là ý tưởng trừu tượng?",
        answers: [
            { text: "Rất đồng ý", type: "S", value: 3 },
            { text: "Đồng ý", type: "S", value: 2 },
            { text: "Không đồng ý", type: "N", value: 2 },
            { text: "Rất không đồng ý", type: "N", value: 3 }
        ]
    },
    {
        id: 3,
        question: "Khi đưa ra quyết định, bạn thường dựa vào logic hơn là cảm xúc?",
        answers: [
            { text: "Rất đồng ý", type: "T", value: 3 },
            { text: "Đồng ý", type: "T", value: 2 },
            { text: "Không đồng ý", type: "F", value: 2 },
            { text: "Rất không đồng ý", type: "F", value: 3 }
        ]
    },
    {
        id: 4,
        question: "Bạn thích có kế hoạch rõ ràng hơn là linh hoạt thay đổi?",
        answers: [
            { text: "Rất đồng ý", type: "J", value: 3 },
            { text: "Đồng ý", type: "J", value: 2 },
            { text: "Không đồng ý", type: "P", value: 2 },
            { text: "Rất không đồng ý", type: "P", value: 3 }
        ]
    },
    {
        id: 5,
        question: "Bạn cảm thấy thoải mái khi là trung tâm chú ý?",
        answers: [
            { text: "Rất đồng ý", type: "E", value: 3 },
            { text: "Đồng ý", type: "E", value: 2 },
            { text: "Không đồng ý", type: "I", value: 2 },
            { text: "Rất không đồng ý", type: "I", value: 3 }
        ]
    }
    // Thêm 15 câu hỏi nữa...
];

// ===== SCHOOL DATABASE =====
const schoolsDatabase = [
    {
        id: 1,
        name: "ĐẠI HỌC CÔNG NGHỆ TPHCM",
        type: "PRIVATE SCHOOL",
        score: 95,
        image: "./image/HUTECH2.jpg",
        location: "TP. Hồ Chí Minh",
        majors: ["Công nghệ thông tin", "Kỹ thuật", "Kinh tế"],
        tuition: "25-35 triệu/năm",
        description: "Trường đại học tư thục hàng đầu về công nghệ"
    },
    {
        id: 2,
        name: "ĐẠI HỌC XÃ HỘI VÀ NHÂN VĂN",
        type: "PUBLIC SCHOOL",
        score: 96,
        image: "./image/nhanvan.png",
        location: "TP. Hồ Chí Minh",
        majors: ["Văn học", "Lịch sử", "Triết học", "Tâm lý học"],
        tuition: "8-12 triệu/năm",
        description: "Trường đại học công lập chuyên về khoa học xã hội"
    },
    {
        id: 3,
        name: "ĐẠI HỌC BÁCH KHOA - HÀ NỘI",
        type: "PUBLIC SCHOOL",
        score: 99,
        image: "./image/bachkhoa.jpg",
        location: "Hà Nội",
        majors: ["Kỹ thuật", "Công nghệ", "Khoa học ứng dụng"],
        tuition: "10-15 triệu/năm",
        description: "Trường đại học kỹ thuật hàng đầu Việt Nam"
    }
];

// ===== CAREER RECOMMENDATIONS =====
const careerRecommendations = {
    "INTJ": {
        careers: ["Kiến trúc sư", "Nhà khoa học", "Kỹ sư phần mềm", "Nhà nghiên cứu"],
        schools: [1, 3],
        description: "Người có tầm nhìn chiến lược, thích làm việc độc lập"
    },
    "ENFP": {
        careers: ["Nhà tư vấn", "Nhà báo", "Giáo viên", "Nhà tâm lý học"],
        schools: [2],
        description: "Người nhiệt tình, sáng tạo và thích giao tiếp"
    },
    "ISTJ": {
        careers: ["Kế toán", "Quản lý", "Luật sư", "Bác sĩ"],
        schools: [1, 2, 3],
        description: "Người thực tế, có trách nhiệm và đáng tin cậy"
    }
    // Thêm các loại MBTI khác...
};

// ===== DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
});

// ===== INITIALIZATION =====
function initializeWebsite() {
    setupEventListeners();
    setupAnimations();
    loadUserProgress();
    
    // Kiểm tra trang hiện tại và khởi tạo chức năng tương ứng
    const currentPage = getCurrentPage();
    
    switch(currentPage) {
        case 'home':
            initializeHomePage();
            break;
        case 'mbti':
            initializeMBTIPage();
            break;
        case 'schools':
            initializeSchoolsPage();
            break;
    }
}

// ===== UTILITY FUNCTIONS =====
function getCurrentPage() {
    const path = window.location.pathname;
    if (path.includes('Home.html') || path === '/') return 'home';
    if (path.includes('MBTI Assessment.html')) return 'mbti';
    if (path.includes('School Recommendations.html')) return 'schools';
    return 'home';
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// ===== EVENT LISTENERS SETUP =====
function setupEventListeners() {
    // Header navigation events
    setupHeaderEvents();
    
    // Contact bar events
    setupContactEvents();
    
    // Feature cards events
    setupFeatureCardEvents();
    
    // Smooth scrolling for anchor links
    setupSmoothScrolling();
}

function setupHeaderEvents() {
    // Logo click - về trang chủ
    const logo = document.querySelector('.logo');
    if (logo) {
        logo.addEventListener('click', () => {
            window.location.href = 'Home.html';
        });
    }
    
    // Get Started buttons
    const getStartedBtns = document.querySelectorAll('.btn-get-started, .btn[href*="MBTI"]');
    getStartedBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            navigateToMBTI();
        });
    });
    
    // Login/Signup buttons
    const loginBtn = document.querySelector('.btn-outline-primary');
    const signupBtn = document.querySelector('.btn-primary');
    
    if (loginBtn) {
        loginBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showLoginModal();
        });
    }
    
    if (signupBtn) {
        signupBtn.addEventListener('click', (e) => {
            e.preventDefault();
            showSignupModal();
        });
    }
}

function setupContactEvents() {
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        item.addEventListener('click', (e) => {
            const href = item.getAttribute('href');
            if (href && href !== '#') {
                // Cho phép hành động mặc định cho tel: và mailto:
                return;
            }
            e.preventDefault();
            showNotification('Chức năng đang được phát triển!', 'info');
        });
    });
}

function setupFeatureCardEvents() {
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.addEventListener('click', () => {
            const features = ['MBTI Assessment', 'Career Suggestions', 'Skill and Course Guidance', 'Progress Tracking'];
            showNotification(`Khám phá ${features[index]}!`);
            
            if (index === 0) { // MBTI Assessment card
                setTimeout(() => navigateToMBTI(), 1000);
            }
        });
    });
}

function setupSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const target = document.querySelector(link.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// ===== NAVIGATION FUNCTIONS =====
function navigateToMBTI() {
    showNotification('Đang chuyển đến bài kiểm tra MBTI...');
    setTimeout(() => {
        window.location.href = 'MBTI Assessment.html';
    }, 1000);
}

function navigateToSchools() {
    showNotification('Đang chuyển đến danh sách trường học...');
    setTimeout(() => {
        window.location.href = 'School Recommendations.html';
    }, 1000);
}

// ===== MODAL FUNCTIONS =====
function showLoginModal() {
    const modal = createModal('Đăng nhập', `
        <form id="loginForm">
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Mật khẩu</label>
                <input type="password" class="form-control" id="password" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">Đăng nhập</button>
        </form>
    `);
    
    document.getElementById('loginForm').addEventListener('submit', (e) => {
        e.preventDefault();
        showNotification('Đăng nhập thành công!');
        modal.hide();
    });
}

function showSignupModal() {
    const modal = createModal('Đăng ký', `
        <form id="signupForm">
            <div class="mb-3">
                <label for="fullName" class="form-label">Họ và tên</label>
                <input type="text" class="form-control" id="fullName" required>
            </div>
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Mật khẩu</label>
                <input type="password" class="form-control" id="password" required>
            </div>
            <div class="mb-3">
                <label for="grade" class="form-label">Lớp</label>
                <select class="form-control" id="grade" required>
                    <option value="">Chọn lớp</option>
                    <option value="10">Lớp 10</option>
                    <option value="11">Lớp 11</option>
                    <option value="12">Lớp 12</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary w-100">Đăng ký</button>
        </form>
    `);
    
    document.getElementById('signupForm').addEventListener('submit', (e) => {
        e.preventDefault();
        showNotification('Đăng ký thành công!');
        modal.hide();
    });
}

function createModal(title, content) {
    const modalHTML = `
        <div class="modal fade" id="dynamicModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('dynamicModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('dynamicModal'));
    modal.show();
    
    return modal;
}

// ===== ANIMATIONS =====
function setupAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .benefit-item, .school-card');
    animateElements.forEach(el => observer.observe(el));
}

// ===== LOCAL STORAGE FUNCTIONS =====
function saveUserProgress() {
    const progress = {
        currentQuestion,
        userAnswers,
        mbtiScores,
        timestamp: Date.now()
    };
    localStorage.setItem('careerGuideProgress', JSON.stringify(progress));
}

function loadUserProgress() {
    const saved = localStorage.getItem('careerGuideProgress');
    if (saved) {
        const progress = JSON.parse(saved);
        // Chỉ load nếu dữ liệu không quá 24 giờ
        if (Date.now() - progress.timestamp < 24 * 60 * 60 * 1000) {
            currentQuestion = progress.currentQuestion || 0;
            userAnswers = progress.userAnswers || [];
            mbtiScores = progress.mbtiScores || { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
        }
    }
}

function clearUserProgress() {
    localStorage.removeItem('careerGuideProgress');
    currentQuestion = 0;
    userAnswers = [];
    mbtiScores = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };
}

// ===== HOME PAGE FUNCTIONS =====
function initializeHomePage() {
    console.log('Initializing Home Page...');

    // Animate hero text
    const heroText = document.querySelector('.hero-text');
    if (heroText) {
        heroText.style.opacity = '0';
        heroText.style.transform = 'translateY(30px)';

        setTimeout(() => {
            heroText.style.transition = 'all 1s ease-out';
            heroText.style.opacity = '1';
            heroText.style.transform = 'translateY(0)';
        }, 500);
    }

    // Counter animation for statistics (if any)
    animateCounters();

    // Setup feature card hover effects
    setupFeatureCardHoverEffects();
}

function animateCounters() {
    const counters = document.querySelectorAll('[data-count]');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-count'));
        let current = 0;
        const increment = target / 100;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };

        updateCounter();
    });
}

function setupFeatureCardHoverEffects() {
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-15px) scale(1.02)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// ===== MBTI ASSESSMENT PAGE FUNCTIONS =====
function initializeMBTIPage() {
    console.log('Initializing MBTI Assessment Page...');

    setupMBTIEventListeners();
    updateQuestionDisplay();
    updateProgressIndicator();
}

function setupMBTIEventListeners() {
    // Answer button events
    const answerBtns = document.querySelectorAll('.answer-btn');
    answerBtns.forEach((btn, index) => {
        btn.addEventListener('click', () => selectAnswer(index));
    });

    // Confirm button event
    const confirmBtn = document.querySelector('.btn-confirm');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', confirmAnswer);
    }

    // Take assessment button
    const takeAssessmentBtn = document.querySelector('.btn-take-assessment');
    if (takeAssessmentBtn) {
        takeAssessmentBtn.addEventListener('click', startAssessment);
    }

    // Reset assessment
    const resetBtn = document.querySelector('.btn-reset');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetAssessment);
    }
}

function selectAnswer(answerIndex) {
    // Remove previous selection
    document.querySelectorAll('.answer-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    // Add selection to clicked button
    const selectedBtn = document.querySelectorAll('.answer-btn')[answerIndex];
    selectedBtn.classList.add('selected');

    // Store selected answer
    const currentQuestionData = mbtiQuestions[currentQuestion];
    if (currentQuestionData) {
        userAnswers[currentQuestion] = {
            questionId: currentQuestionData.id,
            answerIndex: answerIndex,
            answer: currentQuestionData.answers[answerIndex]
        };
    }

    // Enable confirm button
    const confirmBtn = document.querySelector('.btn-confirm');
    if (confirmBtn) {
        confirmBtn.disabled = false;
        confirmBtn.style.opacity = '1';
    }
}

function confirmAnswer() {
    const selectedAnswer = userAnswers[currentQuestion];
    if (!selectedAnswer) {
        showNotification('Vui lòng chọn một câu trả lời!', 'warning');
        return;
    }

    // Update MBTI scores
    const answer = selectedAnswer.answer;
    mbtiScores[answer.type] += answer.value;

    // Save progress
    saveUserProgress();

    // Move to next question
    currentQuestion++;

    if (currentQuestion < totalQuestions && currentQuestion < mbtiQuestions.length) {
        updateQuestionDisplay();
        updateProgressIndicator();
        resetAnswerSelection();
    } else {
        completeAssessment();
    }
}

function updateQuestionDisplay() {
    const questionText = document.querySelector('.question-text');
    const answerBtns = document.querySelectorAll('.answer-btn');

    if (currentQuestion < mbtiQuestions.length) {
        const questionData = mbtiQuestions[currentQuestion];

        if (questionText) {
            questionText.textContent = questionData.question;
        }

        answerBtns.forEach((btn, index) => {
            if (questionData.answers[index]) {
                btn.textContent = questionData.answers[index].text;
                btn.style.display = 'block';
            } else {
                btn.style.display = 'none';
            }
        });
    }
}

function updateProgressIndicator() {
    const progressIndicator = document.querySelector('.progress-indicator');
    if (progressIndicator) {
        progressIndicator.textContent = `${currentQuestion + 1}/${totalQuestions}`;
    }

    // Update progress bar if exists
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        const percentage = ((currentQuestion + 1) / totalQuestions) * 100;
        progressBar.style.width = `${percentage}%`;
    }
}

function resetAnswerSelection() {
    document.querySelectorAll('.answer-btn').forEach(btn => {
        btn.classList.remove('selected');
    });

    const confirmBtn = document.querySelector('.btn-confirm');
    if (confirmBtn) {
        confirmBtn.disabled = true;
        confirmBtn.style.opacity = '0.6';
    }
}

function startAssessment() {
    currentQuestion = 0;
    userAnswers = [];
    mbtiScores = { E: 0, I: 0, S: 0, N: 0, T: 0, F: 0, J: 0, P: 0 };

    updateQuestionDisplay();
    updateProgressIndicator();
    resetAnswerSelection();

    // Scroll to question section
    const questionSection = document.querySelector('.question-section');
    if (questionSection) {
        questionSection.scrollIntoView({ behavior: 'smooth' });
    }

    showNotification('Bài kiểm tra đã bắt đầu! Hãy trả lời thật lòng.');
}

function resetAssessment() {
    if (confirm('Bạn có chắc muốn bắt đầu lại bài kiểm tra?')) {
        clearUserProgress();
        startAssessment();
        showNotification('Bài kiểm tra đã được reset!');
    }
}

function completeAssessment() {
    const mbtiType = calculateMBTIType();
    const recommendation = careerRecommendations[mbtiType];

    showAssessmentResult(mbtiType, recommendation);
    saveAssessmentResult(mbtiType, recommendation);

    // Clear progress after completion
    setTimeout(() => {
        clearUserProgress();
    }, 5000);
}

function calculateMBTIType() {
    let type = '';

    // Extraversion vs Introversion
    type += mbtiScores.E > mbtiScores.I ? 'E' : 'I';

    // Sensing vs Intuition
    type += mbtiScores.S > mbtiScores.N ? 'S' : 'N';

    // Thinking vs Feeling
    type += mbtiScores.T > mbtiScores.F ? 'T' : 'F';

    // Judging vs Perceiving
    type += mbtiScores.J > mbtiScores.P ? 'J' : 'P';

    return type;
}

function showAssessmentResult(mbtiType, recommendation) {
    const resultHTML = `
        <div class="assessment-result">
            <h2>Kết quả MBTI của bạn</h2>
            <div class="mbti-type">${mbtiType}</div>
            <p class="mbti-description">${recommendation ? recommendation.description : 'Mô tả đang được cập nhật'}</p>

            <h3>Nghề nghiệp phù hợp:</h3>
            <ul class="career-list">
                ${recommendation ? recommendation.careers.map(career => `<li>${career}</li>`).join('') : '<li>Đang cập nhật...</li>'}
            </ul>

            <div class="result-actions">
                <button class="btn btn-primary" onclick="navigateToSchools()">Xem trường học phù hợp</button>
                <button class="btn btn-secondary" onclick="resetAssessment()">Làm lại bài kiểm tra</button>
            </div>
        </div>
    `;

    const modal = createModal('Kết quả MBTI', resultHTML);

    // Auto navigate to schools after 10 seconds
    setTimeout(() => {
        if (confirm('Bạn có muốn xem danh sách trường học phù hợp không?')) {
            navigateToSchools();
        }
    }, 10000);
}

function saveAssessmentResult(mbtiType, recommendation) {
    const result = {
        mbtiType,
        recommendation,
        completedAt: new Date().toISOString(),
        scores: mbtiScores
    };

    localStorage.setItem('careerGuideMBTIResult', JSON.stringify(result));
}

// ===== SCHOOL RECOMMENDATIONS PAGE FUNCTIONS =====
function initializeSchoolsPage() {
    console.log('Initializing Schools Page...');

    setupSchoolEventListeners();
    loadRecommendedSchools();
    setupSchoolFilters();
    setupSchoolSearch();
}

function setupSchoolEventListeners() {
    // School card click events
    const schoolCards = document.querySelectorAll('.school-card');
    schoolCards.forEach((card, index) => {
        card.addEventListener('click', () => showSchoolDetails(index));
    });

    // SEE button events
    const seeBtns = document.querySelectorAll('.btn-see');
    seeBtns.forEach((btn, index) => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            showSchoolDetails(index);
        });
    });

    // Filter button event
    const filterBtn = document.querySelector('.btn-filters');
    if (filterBtn) {
        filterBtn.addEventListener('click', showFiltersModal);
    }

    // More button event
    const moreBtn = document.querySelector('.btn-more');
    if (moreBtn) {
        moreBtn.addEventListener('click', loadMoreSchools);
    }

    // Ranking row hover effects
    const rankingRows = document.querySelectorAll('.school-ranking-row');
    rankingRows.forEach(row => {
        row.addEventListener('click', () => {
            const schoolName = row.querySelector('.school-ranking-name').textContent;
            showNotification(`Tìm hiểu thêm về ${schoolName}`);
        });
    });
}

function loadRecommendedSchools() {
    // Load MBTI result to recommend suitable schools
    const mbtiResult = localStorage.getItem('careerGuideMBTIResult');

    if (mbtiResult) {
        const result = JSON.parse(mbtiResult);
        const recommendedSchoolIds = result.recommendation?.schools || [];

        if (recommendedSchoolIds.length > 0) {
            showRecommendationBanner(result.mbtiType, recommendedSchoolIds);
            highlightRecommendedSchools(recommendedSchoolIds);
        }
    }

    // Load and display all schools
    displaySchools(schoolsDatabase);
}

function showRecommendationBanner(mbtiType, schoolIds) {
    const banner = document.createElement('div');
    banner.className = 'recommendation-banner';
    banner.innerHTML = `
        <div class="container">
            <div class="banner-content">
                <h3><i class="fas fa-star"></i> Dành riêng cho bạn (${mbtiType})</h3>
                <p>Dựa trên kết quả MBTI, chúng tôi đề xuất ${schoolIds.length} trường phù hợp với tính cách của bạn.</p>
                <button class="btn btn-light btn-sm" onclick="scrollToRecommended()">Xem ngay</button>
            </div>
        </div>
    `;

    const recommendationsSection = document.querySelector('.recommendations-section');
    if (recommendationsSection) {
        recommendationsSection.after(banner);
    }
}

function highlightRecommendedSchools(schoolIds) {
    const schoolCards = document.querySelectorAll('.school-card');
    schoolCards.forEach((card, index) => {
        if (schoolIds.includes(index + 1)) {
            card.classList.add('recommended');

            // Add recommended badge
            const badge = document.createElement('div');
            badge.className = 'recommended-badge';
            badge.innerHTML = '<i class="fas fa-star"></i> Đề xuất';
            card.appendChild(badge);
        }
    });
}

function scrollToRecommended() {
    const firstRecommended = document.querySelector('.school-card.recommended');
    if (firstRecommended) {
        firstRecommended.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Highlight effect
        firstRecommended.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.6)';
        setTimeout(() => {
            firstRecommended.style.boxShadow = '';
        }, 2000);
    }
}

function displaySchools(schools) {
    // This function would dynamically create school cards
    // For now, we'll work with existing HTML structure
    console.log('Displaying schools:', schools);
}

function showSchoolDetails(schoolIndex) {
    const school = schoolsDatabase[schoolIndex];
    if (!school) return;

    const detailsHTML = `
        <div class="school-details">
            <div class="school-header">
                <img src="${school.image}" alt="${school.name}" class="school-detail-image">
                <div class="school-info">
                    <h2>${school.name}</h2>
                    <p class="school-type-detail">${school.type}</p>
                    <div class="school-score-detail">
                        <span class="score">${school.score}</span>
                        <span class="score-label">Điểm đánh giá</span>
                    </div>
                </div>
            </div>

            <div class="school-content">
                <div class="row">
                    <div class="col-md-6">
                        <h4><i class="fas fa-map-marker-alt"></i> Địa điểm</h4>
                        <p>${school.location}</p>

                        <h4><i class="fas fa-graduation-cap"></i> Ngành học nổi bật</h4>
                        <ul>
                            ${school.majors.map(major => `<li>${major}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h4><i class="fas fa-dollar-sign"></i> Học phí</h4>
                        <p>${school.tuition}</p>

                        <h4><i class="fas fa-info-circle"></i> Mô tả</h4>
                        <p>${school.description}</p>
                    </div>
                </div>

                <div class="school-actions">
                    <button class="btn btn-primary" onclick="addToFavorites(${school.id})">
                        <i class="fas fa-heart"></i> Yêu thích
                    </button>
                    <button class="btn btn-outline-primary" onclick="compareSchool(${school.id})">
                        <i class="fas fa-balance-scale"></i> So sánh
                    </button>
                    <button class="btn btn-success" onclick="getDirections('${school.name}')">
                        <i class="fas fa-directions"></i> Chỉ đường
                    </button>
                </div>
            </div>
        </div>
    `;

    const modal = createModal(school.name, detailsHTML);
}

function showFiltersModal() {
    const filtersHTML = `
        <div class="filters-form">
            <div class="row">
                <div class="col-md-6">
                    <h5>Loại trường</h5>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="public" value="PUBLIC">
                        <label class="form-check-label" for="public">Công lập</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="private" value="PRIVATE">
                        <label class="form-check-label" for="private">Tư thục</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Khu vực</h5>
                    <select class="form-select" id="location">
                        <option value="">Tất cả</option>
                        <option value="Hà Nội">Hà Nội</option>
                        <option value="TP. Hồ Chí Minh">TP. Hồ Chí Minh</option>
                        <option value="Đà Nẵng">Đà Nẵng</option>
                    </select>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <h5>Điểm đánh giá</h5>
                    <input type="range" class="form-range" id="scoreRange" min="0" max="100" value="0">
                    <div class="d-flex justify-content-between">
                        <span>0</span>
                        <span id="scoreValue">0</span>
                        <span>100</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Học phí (triệu/năm)</h5>
                    <select class="form-select" id="tuition">
                        <option value="">Tất cả</option>
                        <option value="0-15">Dưới 15 triệu</option>
                        <option value="15-30">15-30 triệu</option>
                        <option value="30+">Trên 30 triệu</option>
                    </select>
                </div>
            </div>

            <div class="mt-4">
                <button class="btn btn-primary" onclick="applyFilters()">Áp dụng bộ lọc</button>
                <button class="btn btn-secondary" onclick="resetFilters()">Đặt lại</button>
            </div>
        </div>
    `;

    const modal = createModal('Bộ lọc tìm kiếm', filtersHTML);

    // Setup range slider
    const scoreRange = document.getElementById('scoreRange');
    const scoreValue = document.getElementById('scoreValue');

    if (scoreRange && scoreValue) {
        scoreRange.addEventListener('input', () => {
            scoreValue.textContent = scoreRange.value;
        });
    }
}

function setupSchoolFilters() {
    // Add search functionality
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Tìm kiếm trường học...';
    searchInput.className = 'form-control school-search';
    searchInput.addEventListener('input', (e) => filterSchools(e.target.value));

    const filtersBtn = document.querySelector('.btn-filters');
    if (filtersBtn) {
        filtersBtn.parentNode.insertBefore(searchInput, filtersBtn);
    }
}

function setupSchoolSearch() {
    // Real-time search functionality
    let searchTimeout;

    document.addEventListener('input', (e) => {
        if (e.target.classList.contains('school-search')) {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                filterSchools(e.target.value);
            }, 300);
        }
    });
}

function filterSchools(searchTerm) {
    const schoolCards = document.querySelectorAll('.school-card');
    const searchLower = searchTerm.toLowerCase();

    schoolCards.forEach(card => {
        const schoolName = card.querySelector('.school-name').textContent.toLowerCase();
        const schoolType = card.querySelector('.school-type').textContent.toLowerCase();

        if (schoolName.includes(searchLower) || schoolType.includes(searchLower)) {
            card.style.display = 'flex';
            card.style.animation = 'fadeIn 0.3s ease-in';
        } else {
            card.style.display = 'none';
        }
    });

    // Show no results message if needed
    const visibleCards = Array.from(schoolCards).filter(card => card.style.display !== 'none');
    if (visibleCards.length === 0 && searchTerm.trim() !== '') {
        showNotification('Không tìm thấy trường học phù hợp', 'info');
    }
}

function applyFilters() {
    // Get filter values
    const publicChecked = document.getElementById('public')?.checked;
    const privateChecked = document.getElementById('private')?.checked;
    const location = document.getElementById('location')?.value;
    const scoreRange = document.getElementById('scoreRange')?.value;
    const tuition = document.getElementById('tuition')?.value;

    // Apply filters logic here
    console.log('Applying filters:', { publicChecked, privateChecked, location, scoreRange, tuition });

    showNotification('Bộ lọc đã được áp dụng!');

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
    if (modal) modal.hide();
}

function resetFilters() {
    // Reset all filter inputs
    document.querySelectorAll('#dynamicModal input, #dynamicModal select').forEach(input => {
        if (input.type === 'checkbox') {
            input.checked = false;
        } else {
            input.value = '';
        }
    });

    // Show all schools
    document.querySelectorAll('.school-card').forEach(card => {
        card.style.display = 'flex';
    });

    showNotification('Bộ lọc đã được đặt lại!');
}

function loadMoreSchools() {
    showNotification('Đang tải thêm trường học...');

    // Simulate loading more schools
    setTimeout(() => {
        showNotification('Đã tải thêm 5 trường học!');
    }, 1500);
}

// ===== UTILITY FUNCTIONS FOR SCHOOLS =====
function addToFavorites(schoolId) {
    let favorites = JSON.parse(localStorage.getItem('favoriteSchools') || '[]');

    if (!favorites.includes(schoolId)) {
        favorites.push(schoolId);
        localStorage.setItem('favoriteSchools', JSON.stringify(favorites));
        showNotification('Đã thêm vào danh sách yêu thích!');
    } else {
        showNotification('Trường này đã có trong danh sách yêu thích!', 'info');
    }
}

function compareSchool(schoolId) {
    let compareList = JSON.parse(localStorage.getItem('compareSchools') || '[]');

    if (compareList.length >= 3) {
        showNotification('Chỉ có thể so sánh tối đa 3 trường!', 'warning');
        return;
    }

    if (!compareList.includes(schoolId)) {
        compareList.push(schoolId);
        localStorage.setItem('compareSchools', JSON.stringify(compareList));
        showNotification(`Đã thêm vào danh sách so sánh (${compareList.length}/3)`);
    } else {
        showNotification('Trường này đã có trong danh sách so sánh!', 'info');
    }
}

function getDirections(schoolName) {
    const googleMapsUrl = `https://www.google.com/maps/search/${encodeURIComponent(schoolName)}`;
    window.open(googleMapsUrl, '_blank');
    showNotification('Đang mở Google Maps...');
}

// ===== ADDITIONAL UTILITY FUNCTIONS =====
function showComparisonModal() {
    const compareList = JSON.parse(localStorage.getItem('compareSchools') || '[]');

    if (compareList.length < 2) {
        showNotification('Cần ít nhất 2 trường để so sánh!', 'warning');
        return;
    }

    const comparisonHTML = `
        <div class="school-comparison">
            <h4>So sánh trường học</h4>
            <div class="comparison-table">
                <!-- Comparison content would be generated here -->
                <p>Chức năng so sánh đang được phát triển...</p>
            </div>
            <button class="btn btn-danger" onclick="clearComparison()">Xóa danh sách so sánh</button>
        </div>
    `;

    createModal('So sánh trường học', comparisonHTML);
}

function clearComparison() {
    localStorage.removeItem('compareSchools');
    showNotification('Đã xóa danh sách so sánh!');
}

function showFavorites() {
    const favorites = JSON.parse(localStorage.getItem('favoriteSchools') || '[]');

    if (favorites.length === 0) {
        showNotification('Chưa có trường nào trong danh sách yêu thích!', 'info');
        return;
    }

    const favoritesHTML = `
        <div class="favorites-list">
            <h4>Trường yêu thích của bạn</h4>
            <div class="favorites-content">
                ${favorites.map(id => {
                    const school = schoolsDatabase.find(s => s.id === id);
                    return school ? `
                        <div class="favorite-item">
                            <img src="${school.image}" alt="${school.name}" class="favorite-image">
                            <div class="favorite-info">
                                <h5>${school.name}</h5>
                                <p>${school.type}</p>
                            </div>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeFavorite(${id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    ` : '';
                }).join('')}
            </div>
        </div>
    `;

    createModal('Trường yêu thích', favoritesHTML);
}

function removeFavorite(schoolId) {
    let favorites = JSON.parse(localStorage.getItem('favoriteSchools') || '[]');
    favorites = favorites.filter(id => id !== schoolId);
    localStorage.setItem('favoriteSchools', JSON.stringify(favorites));

    showNotification('Đã xóa khỏi danh sách yêu thích!');

    // Refresh favorites modal if open
    const modal = document.getElementById('dynamicModal');
    if (modal && modal.querySelector('.favorites-list')) {
        showFavorites();
    }
}

// ===== KEYBOARD SHORTCUTS =====
document.addEventListener('keydown', (e) => {
    // ESC to close modals
    if (e.key === 'Escape') {
        const modal = bootstrap.Modal.getInstance(document.getElementById('dynamicModal'));
        if (modal) modal.hide();
    }

    // Ctrl+F for search (on schools page)
    if (e.ctrlKey && e.key === 'f' && getCurrentPage() === 'schools') {
        e.preventDefault();
        const searchInput = document.querySelector('.school-search');
        if (searchInput) {
            searchInput.focus();
        }
    }

    // Arrow keys for MBTI navigation
    if (getCurrentPage() === 'mbti') {
        if (e.key === 'ArrowLeft' && currentQuestion > 0) {
            // Previous question (if implemented)
        }
        if (e.key === 'ArrowRight') {
            const confirmBtn = document.querySelector('.btn-confirm');
            if (confirmBtn && !confirmBtn.disabled) {
                confirmAnswer();
            }
        }
    }
});

// ===== PERFORMANCE OPTIMIZATION =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
    showNotification('Đã xảy ra lỗi. Vui lòng thử lại!', 'error');
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e.reason);
    showNotification('Đã xảy ra lỗi kết nối. Vui lòng kiểm tra mạng!', 'error');
});

// ===== ANALYTICS & TRACKING =====
function trackEvent(eventName, eventData = {}) {
    // Track user interactions for analytics
    console.log('Event tracked:', eventName, eventData);

    // Here you would send data to analytics service
    // Example: Google Analytics, Facebook Pixel, etc.
}

function trackPageView() {
    const page = getCurrentPage();
    trackEvent('page_view', { page });
}

// ===== ACCESSIBILITY FEATURES =====
function setupAccessibility() {
    // Add ARIA labels and roles
    const buttons = document.querySelectorAll('button:not([aria-label])');
    buttons.forEach(btn => {
        if (!btn.getAttribute('aria-label')) {
            btn.setAttribute('aria-label', btn.textContent.trim());
        }
    });

    // Add keyboard navigation for cards
    const cards = document.querySelectorAll('.feature-card, .school-card');
    cards.forEach(card => {
        card.setAttribute('tabindex', '0');
        card.setAttribute('role', 'button');

        card.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                card.click();
            }
        });
    });
}

// ===== INITIALIZATION COMPLETE =====
console.log('Career Guide JavaScript loaded successfully! 🎓');

// Track initial page view
document.addEventListener('DOMContentLoaded', () => {
    trackPageView();
    setupAccessibility();
});
