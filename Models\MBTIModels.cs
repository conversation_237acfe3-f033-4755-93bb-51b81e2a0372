using System.ComponentModel.DataAnnotations;

namespace CareerGuide.Models
{
    // MBTI Question Model
    public class MBTIQuestion
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(500)]
        public string QuestionText { get; set; } = string.Empty;
        
        public int QuestionOrder { get; set; }
        
        [Required]
        [StringLength(1)]
        public string Dimension { get; set; } = string.Empty; // E/I, S/N, T/F, J/P
        
        public bool IsActive { get; set; } = true;
        
        public virtual ICollection<MBTIAnswer> Answers { get; set; } = new List<MBTIAnswer>();
    }

    // MBTI Answer Model
    public class MBTIAnswer
    {
        public int Id { get; set; }
        
        public int QuestionId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string AnswerText { get; set; } = string.Empty;
        
        [Required]
        [StringLength(1)]
        public string PersonalityType { get; set; } = string.Empty; // E, I, S, N, T, F, J, P
        
        public int Score { get; set; } // 1-3 points
        
        public int AnswerOrder { get; set; }
        
        public virtual MBTIQuestion Question { get; set; } = null!;
    }

    // MBTI Assessment Session Model
    public class MBTIAssessment
    {
        public int Id { get; set; }
        
        public string SessionId { get; set; } = string.Empty;
        
        public DateTime StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        public string? ResultType { get; set; } // INTJ, ENFP, etc.
        
        public int EScore { get; set; }
        public int IScore { get; set; }
        public int SScore { get; set; }
        public int NScore { get; set; }
        public int TScore { get; set; }
        public int FScore { get; set; }
        public int JScore { get; set; }
        public int PScore { get; set; }
        
        public virtual ICollection<UserAnswer> UserAnswers { get; set; } = new List<UserAnswer>();
    }

    // User Answer Model
    public class UserAnswer
    {
        public int Id { get; set; }
        
        public int AssessmentId { get; set; }
        
        public int QuestionId { get; set; }
        
        public int AnswerId { get; set; }
        
        public DateTime AnsweredAt { get; set; }
        
        public virtual MBTIAssessment Assessment { get; set; } = null!;
        public virtual MBTIQuestion Question { get; set; } = null!;
        public virtual MBTIAnswer Answer { get; set; } = null!;
    }

    // MBTI Personality Type Model
    public class PersonalityType
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(4)]
        public string TypeCode { get; set; } = string.Empty; // INTJ, ENFP, etc.
        
        [Required]
        [StringLength(100)]
        public string TypeName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Strengths { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string Weaknesses { get; set; } = string.Empty;
        
        public virtual ICollection<CareerRecommendation> CareerRecommendations { get; set; } = new List<CareerRecommendation>();
        public virtual ICollection<SchoolRecommendation> SchoolRecommendations { get; set; } = new List<SchoolRecommendation>();
    }

    // Career Recommendation Model
    public class CareerRecommendation
    {
        public int Id { get; set; }
        
        public int PersonalityTypeId { get; set; }
        
        [Required]
        [StringLength(200)]
        public string CareerName { get; set; } = string.Empty;
        
        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        public int MatchPercentage { get; set; } // 0-100
        
        [StringLength(500)]
        public string RequiredSkills { get; set; } = string.Empty;
        
        [StringLength(200)]
        public string SalaryRange { get; set; } = string.Empty;
        
        public virtual PersonalityType PersonalityType { get; set; } = null!;
    }
}
