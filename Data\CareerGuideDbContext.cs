using CareerGuide.Models;
using Microsoft.EntityFrameworkCore;

namespace CareerGuide.Data
{
    public class CareerGuideDbContext : DbContext
    {
        public CareerGuideDbContext(DbContextOptions<CareerGuideDbContext> options) : base(options)
        {
        }

        // MBTI Tables
        public DbSet<MBTIQuestion> MBTIQuestions { get; set; }
        public DbSet<MBTIAnswer> MBTIAnswers { get; set; }
        public DbSet<MBTIAssessment> MBTIAssessments { get; set; }
        public DbSet<UserAnswer> UserAnswers { get; set; }
        public DbSet<PersonalityType> PersonalityTypes { get; set; }
        public DbSet<CareerRecommendation> CareerRecommendations { get; set; }

        // School Tables
        public DbSet<School> Schools { get; set; }
        public DbSet<Major> Majors { get; set; }
        public DbSet<SchoolRecommendation> SchoolRecommendations { get; set; }
        public DbSet<UserFavoriteSchool> UserFavoriteSchools { get; set; }
        public DbSet<SchoolRanking> SchoolRankings { get; set; }
        public DbSet<SchoolStatistics> SchoolStatistics { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure MBTI entities
            ConfigureMBTIEntities(modelBuilder);

            // Configure School entities
            ConfigureSchoolEntities(modelBuilder);

            // Configure indexes for performance
            ConfigureIndexes(modelBuilder);
        }

        private void ConfigureMBTIEntities(ModelBuilder modelBuilder)
        {
            // MBTIQuestion configuration
            modelBuilder.Entity<MBTIQuestion>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.QuestionText).IsRequired().HasMaxLength(500);
                entity.Property(e => e.Dimension).IsRequired().HasMaxLength(1);
                entity.HasIndex(e => e.QuestionOrder);
            });

            // MBTIAnswer configuration
            modelBuilder.Entity<MBTIAnswer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.AnswerText).IsRequired().HasMaxLength(200);
                entity.Property(e => e.PersonalityType).IsRequired().HasMaxLength(1);
                
                entity.HasOne(e => e.Question)
                    .WithMany(q => q.Answers)
                    .HasForeignKey(e => e.QuestionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // MBTIAssessment configuration
            modelBuilder.Entity<MBTIAssessment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SessionId).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ResultType).HasMaxLength(4);
                entity.HasIndex(e => e.SessionId);
                entity.HasIndex(e => e.StartedAt);
            });

            // UserAnswer configuration
            modelBuilder.Entity<UserAnswer>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.Assessment)
                    .WithMany(a => a.UserAnswers)
                    .HasForeignKey(e => e.AssessmentId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Question)
                    .WithMany()
                    .HasForeignKey(e => e.QuestionId)
                    .OnDelete(DeleteBehavior.Restrict);
                
                entity.HasOne(e => e.Answer)
                    .WithMany()
                    .HasForeignKey(e => e.AnswerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // PersonalityType configuration
            modelBuilder.Entity<PersonalityType>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.TypeCode).IsRequired().HasMaxLength(4);
                entity.Property(e => e.TypeName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(1000);
                entity.HasIndex(e => e.TypeCode).IsUnique();
            });

            // CareerRecommendation configuration
            modelBuilder.Entity<CareerRecommendation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CareerName).IsRequired().HasMaxLength(200);
                
                entity.HasOne(e => e.PersonalityType)
                    .WithMany(p => p.CareerRecommendations)
                    .HasForeignKey(e => e.PersonalityTypeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureSchoolEntities(ModelBuilder modelBuilder)
        {
            // School configuration
            modelBuilder.Entity<School>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Location).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Score).HasColumnType("decimal(5,2)");
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Location);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Score);
            });

            // Major configuration
            modelBuilder.Entity<Major>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                
                entity.HasOne(e => e.School)
                    .WithMany(s => s.Majors)
                    .HasForeignKey(e => e.SchoolId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // SchoolRecommendation configuration
            modelBuilder.Entity<SchoolRecommendation>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.PersonalityType)
                    .WithMany(p => p.SchoolRecommendations)
                    .HasForeignKey(e => e.PersonalityTypeId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.School)
                    .WithMany(s => s.SchoolRecommendations)
                    .HasForeignKey(e => e.SchoolId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // UserFavoriteSchool configuration
            modelBuilder.Entity<UserFavoriteSchool>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.SessionId).IsRequired().HasMaxLength(100);
                
                entity.HasOne(e => e.School)
                    .WithMany(s => s.UserFavorites)
                    .HasForeignKey(e => e.SchoolId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasIndex(e => e.SessionId);
            });

            // SchoolRanking configuration
            modelBuilder.Entity<SchoolRanking>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.RankingOrganization).HasMaxLength(100);
                
                entity.HasOne(e => e.School)
                    .WithMany()
                    .HasForeignKey(e => e.SchoolId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // SchoolStatistics configuration
            modelBuilder.Entity<SchoolStatistics>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.EmploymentRate).HasColumnType("decimal(5,2)");
                entity.Property(e => e.AverageSalary).HasColumnType("decimal(15,2)");
                entity.Property(e => e.StudentFacultyRatio).HasColumnType("decimal(5,2)");
                
                entity.HasOne(e => e.School)
                    .WithMany()
                    .HasForeignKey(e => e.SchoolId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Composite indexes for better query performance
            modelBuilder.Entity<UserAnswer>()
                .HasIndex(e => new { e.AssessmentId, e.QuestionId })
                .IsUnique();

            modelBuilder.Entity<SchoolRecommendation>()
                .HasIndex(e => new { e.PersonalityTypeId, e.SchoolId })
                .IsUnique();

            modelBuilder.Entity<UserFavoriteSchool>()
                .HasIndex(e => new { e.SessionId, e.SchoolId })
                .IsUnique();

            modelBuilder.Entity<SchoolRanking>()
                .HasIndex(e => new { e.SchoolId, e.Year });

            modelBuilder.Entity<SchoolStatistics>()
                .HasIndex(e => new { e.SchoolId, e.Year })
                .IsUnique();
        }
    }
}
