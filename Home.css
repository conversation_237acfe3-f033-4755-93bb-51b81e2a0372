/* Custom styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.hero-section {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}
.hero-text {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1.2;
    text-transform: uppercase;
}
.hero-bubbles {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 50%;
    z-index: 1;
}
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #5e50f9;
}
.header {
    background-color: white;
    padding: 15px 0;
    border-radius: 50px;
    margin: 15px auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.header img {
    width: 50px;
    height: auto;
    border-radius: 10px;
}
.btn-outline-primary {
    border-color: #5e50f9;
    color: #5e50f9;
}
.btn-outline-primary:hover {
    background-color: #5e50f9;
    color: white;
}
.btn-primary {
    background-color: #5e50f9;
    border-color: #5e50f9;
}
.btn-primary:hover {
    background-color: #4a3ef7;
    border-color: #4a3ef7;
}
.features-section {
    background-color: #e8f0fe;
    padding: 50px 0;
}
.feature-card {
    text-align: center;
    padding: 20px;
}
.feature-icon {
    font-size: 2.5rem;
    color: #0a2540;
    margin-bottom: 15px;
}
.feature-title {
    font-weight: 600;
    margin-bottom: 10px;
}
.feature-text {
    color: #555;
    font-size: 0.9rem;
}
.benefits-section {
    padding: 70px 0;
}
.benefits-title {
    font-weight: 700;
    color: #0a2540;
    margin-bottom: 30px;
}
.benefit-item {
    margin-bottom: 20px;
}
.benefit-check {
    color: #5e50f9;
    margin-right: 10px;
}
.benefits-section img {
    width: 100%;
    max-width: 500px;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 20px;
}
.footer-section {
    background-color: #1a0f5f;
    color: white;
    padding: 50px 0;
}
.footer-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}
.footer-subtitle {
    font-size: 1.8rem;
    margin-bottom: 20px;
}
.footer-text {
    margin-bottom: 20px;
    font-size: 0.9rem;
}
.footer-section img {
    width: 100%;
    max-width: 400px;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 15px;
}
.contact-bar {
    background-color: white;
    border-radius: 50px;
    padding: 10px 20px;
    margin-top: 20px;
}
.contact-item {
    color: #333;
    text-decoration: none;
    font-weight: 500;
}
.contact-item:hover {
    color: #5e50f9;
}

.position-relative {
    z-index: 2;
}

.logo-large {
    font-size: 2.5rem;
}

.img-fluid {
    border-radius: 15px;
    max-width: 100%;
    height: auto;
}
