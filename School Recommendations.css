/* Custom styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}
.header {
    background-color: #1a0f5f;
    padding: 15px 20px;
    color: white;
}
.header img {
    width: 50px;
    height: auto;
    border-radius: 10px;
}
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #5e50f9;
}
.btn-get-started {
    background-color: white;
    color: #5e50f9;
    border-radius: 50px;
    padding: 8px 25px;
    font-weight: 600;
    border: none;
}
.user-icon {
    background-color: white;
    color: #5e50f9;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}
.hero-section {
    position: relative;
}
.hero-image {
    width: 100%;
    height: 50%;
    margin: 0 auto;
    display: block;
}
.recommendations-section {
    background-color: #e8f0fe;
    padding: 40px 0;
}
.recommendations-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0a2540;
    margin-bottom: 20px;
}
.recommendations-text {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 30px;
}
.btn-filters {
    background-color: white;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 8px 20px;
    font-weight: 500;
}
.btn-filters i {
    margin-right: 10px;
}
.school-list {
    background-color: #1a0f5f;
    padding: 20px 0;
}
.school-card {
    background-color: white;
    border-radius: 15px;
    padding: 15px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    position: relative;
}
.school-image {
    width: 100%;
    max-width: 150px;
    height: auto;
    border-radius: 15px;
    margin-right: 20px;
}
.school-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}
.school-type {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    margin-bottom: 10px;
}
.school-score {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    position: absolute;
    left: 150px;
    bottom: 15px;
}
.btn-see {
    background-color: #5e50f9;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 10px 30px;
    font-weight: 600;
    position: absolute;
    right: 20px;
    bottom: 20px;
    transition: background-color 0.3s;
}
.btn-see:hover {
    background-color: #4a3ef7;
}
.arrow-marker {
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 1.5rem;
}
.btn-more {
    background-color: white;
    color: #1a0f5f;
    border-radius: 50px;
    padding: 8px 30px;
    font-weight: 600;
    margin: 20px auto;
    display: block;
}
.top-schools-section {
    background-color: #1a0f5f;
    color: white;
    padding: 30px 0;
}
.top-schools-title {
    font-size: 1.8rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
}
.school-ranking-table {
    background-color: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.school-ranking-row {
    display: flex;
    border-bottom: 1px solid #eee;
}
.school-ranking-name {
    flex: 1;
    padding: 15px;
    text-align: right;
    font-weight: 500;
    color: #333;
}
.school-ranking-score {
    width: 40%;
    background-color: #7dd3fc;
    padding: 15px;
    text-align: right;
    font-weight: 700;
    color: #333;
}
.contact-bar {
    background-color: white;
    border-radius: 50px;
    padding: 10px 20px;
    margin: 20px auto;
}
.contact-item {
    color: #333;
    text-decoration: none;
    font-weight: 500;
}
.contact-item:hover {
    color: #5e50f9;
}
