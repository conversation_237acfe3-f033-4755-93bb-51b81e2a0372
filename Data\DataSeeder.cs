using CareerGuide.Models;

namespace CareerGuide.Data
{
    public static class DataSeeder
    {
        public static void SeedData(CareerGuideDbContext context)
        {
            // Seed Personality Types
            SeedPersonalityTypes(context);
            
            // Seed MBTI Questions and Answers
            SeedMBTIQuestions(context);
            
            // Seed Schools
            SeedSchools(context);
            
            // Seed Career Recommendations
            SeedCareerRecommendations(context);
            
            // Seed School Recommendations
            SeedSchoolRecommendations(context);
            
            context.SaveChanges();
        }

        private static void SeedPersonalityTypes(CareerGuideDbContext context)
        {
            if (context.PersonalityTypes.Any()) return;

            var personalityTypes = new List<PersonalityType>
            {
                new PersonalityType
                {
                    TypeCode = "INTJ",
                    TypeName = "Kiến trúc sư",
                    Description = "Người có tầm nhìn chiến lược, thích làm việc độc lập và có khả năng tư duy hệ thống.",
                    Strengths = "Tư duy logic, độc lập, c<PERSON> tầm nhìn xa, quyế<PERSON> đo<PERSON>",
                    Weaknesses = "<PERSON><PERSON> thể quá cứng nhắ<PERSON>, kh<PERSON> tiế<PERSON> cận, thiếu kiên nhẫn với chi tiết"
                },
                new PersonalityType
                {
                    TypeCode = "ENFP",
                    TypeName = "Nhà vận động",
                    Description = "Người nhiệt tình, sáng tạo và thích giao tiếp với mọi người.",
                    Strengths = "Nhiệt tình, sáng tạo, linh hoạt, giao tiếp tốt",
                    Weaknesses = "Dễ mất tập trung, khó hoàn thành công việc, quá lạc quan"
                },
                new PersonalityType
                {
                    TypeCode = "ISTJ",
                    TypeName = "Người bảo vệ",
                    Description = "Người thực tế, có trách nhiệm và đáng tin cậy.",
                    Strengths = "Có trách nhiệm, đáng tin cậy, thực tế, có tổ chức",
                    Weaknesses = "Cứng nhắc, khó thích nghi, quá thận trọng"
                },
                new PersonalityType
                {
                    TypeCode = "ESFP",
                    TypeName = "Người giải trí",
                    Description = "Người năng động, thân thiện và thích khám phá những điều mới.",
                    Strengths = "Thân thiện, linh hoạt, thực tế, nhiệt tình",
                    Weaknesses = "Dễ bị phân tâm, thiếu kỷ luật, tránh xung đột"
                }
            };

            context.PersonalityTypes.AddRange(personalityTypes);
        }

        private static void SeedMBTIQuestions(CareerGuideDbContext context)
        {
            if (context.MBTIQuestions.Any()) return;

            var questions = new List<MBTIQuestion>
            {
                new MBTIQuestion
                {
                    QuestionText = "Bạn có thấy dễ dàng giới thiệu bản thân với người khác không?",
                    QuestionOrder = 1,
                    Dimension = "E",
                    IsActive = true
                },
                new MBTIQuestion
                {
                    QuestionText = "Bạn thích làm việc với các chi tiết cụ thể hơn là ý tưởng trừu tượng?",
                    QuestionOrder = 2,
                    Dimension = "S",
                    IsActive = true
                },
                new MBTIQuestion
                {
                    QuestionText = "Khi đưa ra quyết định, bạn thường dựa vào logic hơn là cảm xúc?",
                    QuestionOrder = 3,
                    Dimension = "T",
                    IsActive = true
                },
                new MBTIQuestion
                {
                    QuestionText = "Bạn thích có kế hoạch rõ ràng hơn là linh hoạt thay đổi?",
                    QuestionOrder = 4,
                    Dimension = "J",
                    IsActive = true
                },
                new MBTIQuestion
                {
                    QuestionText = "Bạn cảm thấy thoải mái khi là trung tâm chú ý?",
                    QuestionOrder = 5,
                    Dimension = "E",
                    IsActive = true
                }
            };

            context.MBTIQuestions.AddRange(questions);
            context.SaveChanges();

            // Add answers for each question
            var questionIds = context.MBTIQuestions.Select(q => q.Id).ToList();
            var answers = new List<MBTIAnswer>();

            foreach (var questionId in questionIds)
            {
                var question = context.MBTIQuestions.Find(questionId);
                if (question != null)
                {
                    var dimension = question.Dimension;
                    var oppositeDimension = GetOppositeDimension(dimension);

                    answers.AddRange(new List<MBTIAnswer>
                    {
                        new MBTIAnswer
                        {
                            QuestionId = questionId,
                            AnswerText = "Rất đồng ý",
                            PersonalityType = dimension,
                            Score = 3,
                            AnswerOrder = 1
                        },
                        new MBTIAnswer
                        {
                            QuestionId = questionId,
                            AnswerText = "Đồng ý",
                            PersonalityType = dimension,
                            Score = 2,
                            AnswerOrder = 2
                        },
                        new MBTIAnswer
                        {
                            QuestionId = questionId,
                            AnswerText = "Không đồng ý",
                            PersonalityType = oppositeDimension,
                            Score = 2,
                            AnswerOrder = 3
                        },
                        new MBTIAnswer
                        {
                            QuestionId = questionId,
                            AnswerText = "Rất không đồng ý",
                            PersonalityType = oppositeDimension,
                            Score = 3,
                            AnswerOrder = 4
                        }
                    });
                }
            }

            context.MBTIAnswers.AddRange(answers);
        }

        private static string GetOppositeDimension(string dimension)
        {
            return dimension switch
            {
                "E" => "I",
                "I" => "E",
                "S" => "N",
                "N" => "S",
                "T" => "F",
                "F" => "T",
                "J" => "P",
                "P" => "J",
                _ => dimension
            };
        }

        private static void SeedSchools(CareerGuideDbContext context)
        {
            if (context.Schools.Any()) return;

            var schools = new List<School>
            {
                new School
                {
                    Name = "ĐẠI HỌC CÔNG NGHỆ TPHCM",
                    Type = "PRIVATE",
                    Location = "TP. Hồ Chí Minh",
                    Score = 95,
                    ImageUrl = "/images/schools/hutech.jpg",
                    Description = "Trường đại học tư thục hàng đầu về công nghệ tại Việt Nam",
                    TuitionRange = "25-35 triệu/năm",
                    Website = "https://www.hutech.edu.vn",
                    Phone = "028-5445-7777",
                    Email = "<EMAIL>",
                    Address = "475A Điện Biên Phủ, P.25, Q.Bình Thạnh, TP.HCM",
                    EstablishedYear = new DateTime(1995, 1, 1),
                    StudentCount = 25000,
                    IsActive = true
                },
                new School
                {
                    Name = "ĐẠI HỌC XÃ HỘI VÀ NHÂN VĂN",
                    Type = "PUBLIC",
                    Location = "TP. Hồ Chí Minh",
                    Score = 96,
                    ImageUrl = "/images/schools/nhanvan.jpg",
                    Description = "Trường đại học công lập chuyên về khoa học xã hội và nhân văn",
                    TuitionRange = "8-12 triệu/năm",
                    Website = "https://www.hcmussh.edu.vn",
                    Phone = "028-3855-4269",
                    Email = "<EMAIL>",
                    Address = "10-12 Đinh Tiên Hoàng, P.1, Q.Bình Thạnh, TP.HCM",
                    EstablishedYear = new DateTime(1957, 1, 1),
                    StudentCount = 18000,
                    IsActive = true
                },
                new School
                {
                    Name = "ĐẠI HỌC BÁCH KHOA - HÀ NỘI",
                    Type = "PUBLIC",
                    Location = "Hà Nội",
                    Score = 99,
                    ImageUrl = "/images/schools/bachkhoa.jpg",
                    Description = "Trường đại học kỹ thuật hàng đầu Việt Nam",
                    TuitionRange = "10-15 triệu/năm",
                    Website = "https://www.hust.edu.vn",
                    Phone = "024-3868-3008",
                    Email = "<EMAIL>",
                    Address = "1 Đại Cồ Việt, Hai Bà Trưng, Hà Nội",
                    EstablishedYear = new DateTime(1956, 1, 1),
                    StudentCount = 35000,
                    IsActive = true
                }
            };

            context.Schools.AddRange(schools);
            context.SaveChanges();

            // Add majors for each school
            SeedMajors(context);
        }

        private static void SeedMajors(CareerGuideDbContext context)
        {
            var schools = context.Schools.ToList();
            var majors = new List<Major>();

            foreach (var school in schools)
            {
                if (school.Name.Contains("CÔNG NGHỆ"))
                {
                    majors.AddRange(new List<Major>
                    {
                        new Major { SchoolId = school.Id, Name = "Công nghệ thông tin", Description = "Đào tạo chuyên gia IT", Duration = "4 năm", TuitionFee = "30 triệu/năm", AdmissionScore = 24, CareerProspects = "Lập trình viên, Kỹ sư phần mềm", IsPopular = true },
                        new Major { SchoolId = school.Id, Name = "Kỹ thuật điện tử", Description = "Đào tạo kỹ sư điện tử", Duration = "4 năm", TuitionFee = "28 triệu/năm", AdmissionScore = 23, CareerProspects = "Kỹ sư điện tử, Thiết kế mạch", IsPopular = false },
                        new Major { SchoolId = school.Id, Name = "Kinh tế", Description = "Đào tạo chuyên gia kinh tế", Duration = "4 năm", TuitionFee = "25 triệu/năm", AdmissionScore = 22, CareerProspects = "Chuyên viên kinh tế, Phân tích tài chính", IsPopular = true }
                    });
                }
                else if (school.Name.Contains("XÃ HỘI"))
                {
                    majors.AddRange(new List<Major>
                    {
                        new Major { SchoolId = school.Id, Name = "Văn học", Description = "Đào tạo chuyên gia văn học", Duration = "4 năm", TuitionFee = "10 triệu/năm", AdmissionScore = 20, CareerProspects = "Giáo viên, Nhà báo", IsPopular = true },
                        new Major { SchoolId = school.Id, Name = "Tâm lý học", Description = "Đào tạo chuyên gia tâm lý", Duration = "4 năm", TuitionFee = "12 triệu/năm", AdmissionScore = 22, CareerProspects = "Tư vấn tâm lý, Nghiên cứu", IsPopular = true },
                        new Major { SchoolId = school.Id, Name = "Lịch sử", Description = "Đào tạo chuyên gia lịch sử", Duration = "4 năm", TuitionFee = "9 triệu/năm", AdmissionScore = 19, CareerProspects = "Giáo viên, Nghiên cứu viên", IsPopular = false }
                    });
                }
                else if (school.Name.Contains("BÁCH KHOA"))
                {
                    majors.AddRange(new List<Major>
                    {
                        new Major { SchoolId = school.Id, Name = "Kỹ thuật cơ khí", Description = "Đào tạo kỹ sư cơ khí", Duration = "4 năm", TuitionFee = "12 triệu/năm", AdmissionScore = 26, CareerProspects = "Kỹ sư cơ khí, Thiết kế sản phẩm", IsPopular = true },
                        new Major { SchoolId = school.Id, Name = "Kỹ thuật xây dựng", Description = "Đào tạo kỹ sư xây dựng", Duration = "4 năm", TuitionFee = "13 triệu/năm", AdmissionScore = 25, CareerProspects = "Kỹ sư xây dựng, Quản lý dự án", IsPopular = true },
                        new Major { SchoolId = school.Id, Name = "Công nghệ thông tin", Description = "Đào tạo chuyên gia IT", Duration = "4 năm", TuitionFee = "14 triệu/năm", AdmissionScore = 28, CareerProspects = "Lập trình viên, Kỹ sư phần mềm", IsPopular = true }
                    });
                }
            }

            context.Majors.AddRange(majors);
        }

        private static void SeedCareerRecommendations(CareerGuideDbContext context)
        {
            if (context.CareerRecommendations.Any()) return;

            var personalityTypes = context.PersonalityTypes.ToList();
            var careerRecommendations = new List<CareerRecommendation>();

            foreach (var personalityType in personalityTypes)
            {
                switch (personalityType.TypeCode)
                {
                    case "INTJ":
                        careerRecommendations.AddRange(new List<CareerRecommendation>
                        {
                            new CareerRecommendation { PersonalityTypeId = personalityType.Id, CareerName = "Kiến trúc sư", Description = "Thiết kế và lập kế hoạch xây dựng", MatchPercentage = 95, RequiredSkills = "Tư duy không gian, Sáng tạo, Kỹ thuật", SalaryRange = "15-50 triệu/tháng" },
                            new CareerRecommendation { PersonalityTypeId = personalityType.Id, CareerName = "Kỹ sư phần mềm", Description = "Phát triển ứng dụng và hệ thống", MatchPercentage = 90, RequiredSkills = "Lập trình, Logic, Giải quyết vấn đề", SalaryRange = "20-80 triệu/tháng" },
                            new CareerRecommendation { PersonalityTypeId = personalityType.Id, CareerName = "Nhà nghiên cứu", Description = "Nghiên cứu khoa học và phát triển", MatchPercentage = 85, RequiredSkills = "Phân tích, Nghiên cứu, Viết báo cáo", SalaryRange = "12-40 triệu/tháng" }
                        });
                        break;
                    case "ENFP":
                        careerRecommendations.AddRange(new List<CareerRecommendation>
                        {
                            new CareerRecommendation { PersonalityTypeId = personalityType.Id, CareerName = "Nhà tư vấn", Description = "Tư vấn và hỗ trợ khách hàng", MatchPercentage = 92, RequiredSkills = "Giao tiếp, Lắng nghe, Tư vấn", SalaryRange = "10-35 triệu/tháng" },
                            new CareerRecommendation { PersonalityTypeId = personalityType.Id, CareerName = "Nhà báo", Description = "Thu thập và truyền tải thông tin", MatchPercentage = 88, RequiredSkills = "Viết lách, Giao tiếp, Tò mò", SalaryRange = "8-25 triệu/tháng" },
                            new CareerRecommendation { PersonalityTypeId = personalityType.Id, CareerName = "Giáo viên", Description = "Giảng dạy và giáo dục", MatchPercentage = 85, RequiredSkills = "Sư phạm, Kiên nhẫn, Truyền đạt", SalaryRange = "7-20 triệu/tháng" }
                        });
                        break;
                }
            }

            context.CareerRecommendations.AddRange(careerRecommendations);
        }

        private static void SeedSchoolRecommendations(CareerGuideDbContext context)
        {
            if (context.SchoolRecommendations.Any()) return;

            var personalityTypes = context.PersonalityTypes.ToList();
            var schools = context.Schools.ToList();
            var schoolRecommendations = new List<SchoolRecommendation>();

            foreach (var personalityType in personalityTypes)
            {
                foreach (var school in schools)
                {
                    var score = CalculateRecommendationScore(personalityType.TypeCode, school);
                    if (score > 70)
                    {
                        schoolRecommendations.Add(new SchoolRecommendation
                        {
                            PersonalityTypeId = personalityType.Id,
                            SchoolId = school.Id,
                            RecommendationScore = score,
                            Reason = GetRecommendationReason(personalityType.TypeCode, school)
                        });
                    }
                }
            }

            context.SchoolRecommendations.AddRange(schoolRecommendations);
        }

        private static int CalculateRecommendationScore(string personalityType, School school)
        {
            // Simple logic for demonstration
            var baseScore = (int)school.Score;
            
            if (personalityType == "INTJ" && school.Name.Contains("CÔNG NGHỆ"))
                return Math.Min(100, baseScore + 5);
            
            if (personalityType == "ENFP" && school.Name.Contains("XÃ HỘI"))
                return Math.Min(100, baseScore + 3);
            
            return Math.Max(70, baseScore - 10);
        }

        private static string GetRecommendationReason(string personalityType, School school)
        {
            if (personalityType == "INTJ" && school.Name.Contains("CÔNG NGHỆ"))
                return "Phù hợp với tính cách thích công nghệ và tư duy hệ thống";

            if (personalityType == "ENFP" && school.Name.Contains("XÃ HỘI"))
                return "Phù hợp với tính cách thích giao tiếp và làm việc với con người";

            return "Trường có uy tín và chất lượng đào tạo tốt";
        }
    }
}
