<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MBTI Assessment - Career Guide</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Replace inline CSS with external CSS file -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <div class="header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            <img src="./image/logo.png" alt="Logo" class="me-2">
            <span class="logo">CARRER GUIDE</span>
        </div>
        <div class="d-flex align-items-center">
            <button class="btn-get-started">GET STARTERD</button>
            <div class="user-icon">
                <i class="fas fa-user"></i>
            </div>
        </div>
    </div>

    <!-- Hero Section with Person Image -->
    <div class="hero-section">
        <img src="./image/homesection.png" alt="Person studying" class="hero-image">
    </div>

    <!-- Assessment Header -->
    <div class="assessment-header">
        <h1 class="assessment-title">TAKE THE MBTI ASSESSMENT</h1>
        <p class="assessment-subtitle">ANSWER EACH QUESTION ACCORDING<br>TO YOUR PERSONALITY</p>
        <div class="progress-indicator">
            1/20
        </div>
    </div>

    <!-- Career Path Section -->
    <section class="career-path-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="career-path-title">Find Your<br>Ideal Career Path</h2>
                    <p class="career-path-text">Discover careers that match your<br>personality based on the MBTI test</p>
                    <button class="btn-take-assessment">Take the Assessment</button>
                </div>
                <div class="col-md-6">
                    <img src="./image/studentssmile.png" alt="Student smiling" class="img-fluid">
                </div>
            </div>
        </div>
    </section>

    <!-- Question Section -->
    <section class="question-section">
        <div class="container">
            <h2 class="question-text">YOU FIND IT EASY TO INTRODUCE<br>YOURSELF TO OTHER PEOPLE</h2>
            
            <div class="row justify-content-center">
                <div class="col-md-5 mb-3">
                    <button class="answer-btn">Strongly agree</button>
                </div>
                <div class="col-md-5 mb-3">
                    <button class="answer-btn">Strongly disagree</button>
                </div>
            </div>
            
            <div class="row justify-content-center">
                <div class="col-md-5 mb-3">
                    <button class="answer-btn">Disagree</button>
                </div>
                <div class="col-md-5 mb-3">
                    <button class="answer-btn">Agree</button>
                </div>
            </div>
            
            <button class="btn-confirm">Confirm</button>
        </div>
    </section>

    <!-- Contact Bar -->
    <div class="container">
        <div class="contact-bar d-flex justify-content-between align-items-center">
            <a href="tel:199122160" class="contact-item">
                <i class="fas fa-phone-alt me-2"></i>
                199122160
            </a>
            <a href="mailto:<EMAIL>" class="contact-item">
                <i class="fas fa-envelope me-2"></i>
                <EMAIL>
            </a>
            <a href="#" class="contact-item">
                <i class="fab fa-facebook-square me-2"></i>
                carrerguide
            </a>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>