/* Custom styles */
body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
}
.header {
    background-color: #1a0f5f;
    padding: 15px 20px;
    color: white;
}
.header img {
    width: 50px;
    height: auto;
    border-radius: 10px;
    margin: 0 auto;
    display: block;
}
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #5e50f9;
}
.btn-get-started {
    background-color: white;
    color: #5e50f9;
    border-radius: 50px;
    padding: 8px 25px;
    font-weight: 600;
    border: none;
}
.user-icon {
    background-color: white;
    color: #5e50f9;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}
.hero-section {
    position: relative;
}
.hero-image {
    width: 100%;
    height: auto;
    margin: 0 auto;
    display: block;
}
.assessment-header {
    background-color: #1a0f5f;
    color: white;
    padding: 30px 20px;
    text-align: center;
}
.assessment-title {
    font-size: 2.5rem;
    font-weight: 800;
    text-transform: uppercase;
    margin-bottom: 10px;
}
.assessment-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    text-transform: uppercase;
}
.progress-indicator {
    background-color: white;
    color: #333;
    font-weight: 600;
    font-size: 1.2rem;
    padding: 8px 30px;
    border-radius: 50px;
    display: inline-block;
    margin-top: 20px;
}
.career-path-section {
    background-color: #e8f0fe;
    padding: 50px 0;
}
.career-path-section img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 15px;
    margin: 0 auto;
    display: block;
}
.career-path-title {
    font-size: 3rem;
    font-weight: 700;
    color: #0a2540;
    margin-bottom: 20px;
}
.career-path-text {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 30px;
}
.btn-take-assessment {
    background-color: #0a2540;
    color: white;
    border-radius: 5px;
    padding: 12px 25px;
    font-weight: 600;
    border: none;
    font-size: 1.1rem;
}
.question-section {
    background-color: #1a0f5f;
    color: white;
    padding: 50px 20px;
    text-align: center;
}
.question-section img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 10px;
    margin: 0 auto;
    display: block;
}
.question-text {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 40px;
}
.answer-btn {
    background-color: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 50px;
    padding: 12px 30px;
    font-size: 1.1rem;
    margin: 10px;
    width: 100%;
    transition: all 0.3s;
}
.answer-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
.btn-confirm {
    background-color: white;
    color: #1a0f5f;
    border-radius: 50px;
    padding: 12px 40px;
    font-weight: 600;
    font-size: 1.1rem;
    margin-top: 30px;
    border: none;
}
.contact-bar {
    background-color: white;
    border-radius: 50px;
    padding: 10px 20px;
    margin: 20px auto;
}
.contact-item {
    color: #333;
    text-decoration: none;
    font-weight: 500;
}
.contact-item:hover {
    color: #5e50f9;
}
