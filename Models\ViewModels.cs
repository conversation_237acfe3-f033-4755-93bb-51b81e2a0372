using System.ComponentModel.DataAnnotations;

namespace CareerGuide.Models.ViewModels
{
    // Home Page ViewModel
    public class HomeViewModel
    {
        public string WelcomeMessage { get; set; } = "<PERSON>hám phá đam mê, định hướng tương lai";
        public List<FeatureViewModel> Features { get; set; } = new();
        public List<TestimonialViewModel> Testimonials { get; set; } = new();
        public StatisticsViewModel Statistics { get; set; } = new();
    }

    public class FeatureViewModel
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string IconClass { get; set; } = string.Empty;
        public string ActionUrl { get; set; } = string.Empty;
    }

    public class TestimonialViewModel
    {
        public string Name { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Avatar { get; set; } = string.Empty;
        public string School { get; set; } = string.Empty;
    }

    public class StatisticsViewModel
    {
        public int TotalUsers { get; set; }
        public int TotalSchools { get; set; }
        public int CompletedAssessments { get; set; }
        public int SuccessfulMatches { get; set; }
    }

    // MBTI Assessment ViewModels
    public class MBTIAssessmentViewModel
    {
        public int AssessmentId { get; set; }
        public int CurrentQuestionIndex { get; set; }
        public int TotalQuestions { get; set; }
        public MBTIQuestionViewModel? CurrentQuestion { get; set; }
        public int ProgressPercentage => TotalQuestions > 0 ? (CurrentQuestionIndex * 100) / TotalQuestions : 0;
        public bool IsCompleted { get; set; }
    }

    public class MBTIQuestionViewModel
    {
        public int Id { get; set; }
        public string QuestionText { get; set; } = string.Empty;
        public List<MBTIAnswerViewModel> Answers { get; set; } = new();
        public int QuestionNumber { get; set; }
        public string Dimension { get; set; } = string.Empty;
    }

    public class MBTIAnswerViewModel
    {
        public int Id { get; set; }
        public string AnswerText { get; set; } = string.Empty;
        public string PersonalityType { get; set; } = string.Empty;
        public int Score { get; set; }
    }

    public class MBTIResultViewModel
    {
        public string PersonalityType { get; set; } = string.Empty;
        public string TypeName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Strengths { get; set; } = string.Empty;
        public string Weaknesses { get; set; } = string.Empty;
        public List<CareerRecommendationViewModel> CareerRecommendations { get; set; } = new();
        public List<SchoolRecommendationViewModel> SchoolRecommendations { get; set; } = new();
        public MBTIScoresViewModel Scores { get; set; } = new();
    }

    public class MBTIScoresViewModel
    {
        public int EScore { get; set; }
        public int IScore { get; set; }
        public int SScore { get; set; }
        public int NScore { get; set; }
        public int TScore { get; set; }
        public int FScore { get; set; }
        public int JScore { get; set; }
        public int PScore { get; set; }

        public string ExtraversionIntroversion => EScore > IScore ? "E" : "I";
        public string SensingIntuition => SScore > NScore ? "S" : "N";
        public string ThinkingFeeling => TScore > FScore ? "T" : "F";
        public string JudgingPerceiving => JScore > PScore ? "J" : "P";
        
        public string PersonalityType => $"{ExtraversionIntroversion}{SensingIntuition}{ThinkingFeeling}{JudgingPerceiving}";
    }

    public class CareerRecommendationViewModel
    {
        public string CareerName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int MatchPercentage { get; set; }
        public string RequiredSkills { get; set; } = string.Empty;
        public string SalaryRange { get; set; } = string.Empty;
    }

    // School ViewModels
    public class SchoolListViewModel
    {
        public List<SchoolCardViewModel> Schools { get; set; } = new();
        public SchoolFilterViewModel Filters { get; set; } = new();
        public PaginationViewModel Pagination { get; set; } = new();
        public string? RecommendedForPersonality { get; set; }
        public List<SchoolCardViewModel> RecommendedSchools { get; set; } = new();
    }

    public class SchoolCardViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string TuitionRange { get; set; } = string.Empty;
        public List<string> PopularMajors { get; set; } = new();
        public bool IsRecommended { get; set; }
        public bool IsFavorite { get; set; }
        public int RecommendationScore { get; set; }
    }

    public class SchoolDetailViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string TuitionRange { get; set; } = string.Empty;
        public string Website { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public DateTime EstablishedYear { get; set; }
        public int StudentCount { get; set; }
        public List<MajorViewModel> Majors { get; set; } = new();
        public SchoolStatisticsViewModel? Statistics { get; set; }
        public List<SchoolRankingViewModel> Rankings { get; set; } = new();
        public bool IsFavorite { get; set; }
    }

    public class MajorViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Duration { get; set; } = string.Empty;
        public string TuitionFee { get; set; } = string.Empty;
        public int AdmissionScore { get; set; }
        public string CareerProspects { get; set; } = string.Empty;
        public bool IsPopular { get; set; }
    }

    public class SchoolStatisticsViewModel
    {
        public int Year { get; set; }
        public int TotalStudents { get; set; }
        public int NewStudents { get; set; }
        public int Graduates { get; set; }
        public decimal EmploymentRate { get; set; }
        public decimal AverageSalary { get; set; }
        public int TotalFaculty { get; set; }
        public decimal StudentFacultyRatio { get; set; }
    }

    public class SchoolRankingViewModel
    {
        public int Year { get; set; }
        public int NationalRank { get; set; }
        public int RegionalRank { get; set; }
        public string RankingOrganization { get; set; } = string.Empty;
    }

    public class SchoolRecommendationViewModel
    {
        public int SchoolId { get; set; }
        public string SchoolName { get; set; } = string.Empty;
        public string SchoolType { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public decimal Score { get; set; }
        public string ImageUrl { get; set; } = string.Empty;
        public int RecommendationScore { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    public class SchoolFilterViewModel
    {
        public string? SearchTerm { get; set; }
        public string? Type { get; set; }
        public string? Location { get; set; }
        public decimal? MinScore { get; set; }
        public decimal? MaxScore { get; set; }
        public string? TuitionRange { get; set; }
        public List<string> SelectedMajors { get; set; } = new();
        public string SortBy { get; set; } = "Score"; // Score, Name, Location
        public string SortOrder { get; set; } = "desc"; // asc, desc
    }

    public class PaginationViewModel
    {
        public int CurrentPage { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalItems { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalItems / PageSize);
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
    }

    // API Response ViewModels
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class SubmitAnswerRequest
    {
        [Required]
        public int AssessmentId { get; set; }
        
        [Required]
        public int QuestionId { get; set; }
        
        [Required]
        public int AnswerId { get; set; }
    }
}
