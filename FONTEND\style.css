body {
    font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f9f9f9;
    color: #333;
    margin: 0;
    padding: 0;
}
.hero-section {
    background: url(./image/bia.png) no-repeat center center/cover;
    color: white;
    padding: 120px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}
.hero-text {
    font-size: 5rem;
    font-weight: 900;
    line-height: 1.2;
    text-transform: uppercase;
    text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.4);
    margin-bottom: 20px;
}
.hero-bubbles {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 50%;
    z-index: 1;
}
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #5e50f9;
}
.header {
    background-color: #6595c5; 
    padding: 20px 30px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    margin-bottom: 20px;
    margin-top: 20px; 
}
.header img {
    width: 60px;
    height: auto;
    border-radius: 15px;
}
.header .logo {
    font-size: 2rem;
    font-weight: 800;
    color: #ecf0f1; 
    text-transform: uppercase;
}
.header .btn-login,
.header .btn-signup {
    background-color: transparent;
    border: 2px solid #ecf0f1;
    color: #ecf0f1;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
    margin-left: 10px;
}
.header .btn-login:hover,
.header .btn-signup:hover {
    background-color: #ecf0f1;
    color: #2c3e50; 
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.btn-outline-primary {
    border-color: #5e50f9;
    color: #5e50f9;
    padding: 10px 20px;
    border-radius: 30px;
    transition: all 0.3s ease;
}
.btn-outline-primary:hover {
    background-color: #5e50f9;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.btn-primary {
    background-color: #5e50f9;
    border-color: #5e50f9;
}
.btn-primary:hover {
    background-color: #4a3ef7;
    border-color: #4a3ef7;
}
.features-section {
    background-color: #e8f0fe;
    padding: 60px 0;
    text-align: center;
}
.feature-card {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}
.feature-card:hover {
    transform: translateY(-10px);
}
.feature-icon {
    font-size: 3rem;
    color: #0a2540;
    margin-bottom: 20px;
}
.feature-title {
    font-weight: 600;
    margin-bottom: 10px;
}
.feature-text {
    color: #555;
    font-size: 0.9rem;
}
.benefits-section {
    padding: 70px 0;
}
.benefits-title {
    font-weight: 700;
    color: #0a2540;
    margin-bottom: 30px;
}
.benefit-item {
    margin-bottom: 20px;
}
.benefit-check {
    color: #5e50f9;
    margin-right: 10px;
}
.benefits-section img {
    width: 100%;
    max-width: 500px;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 20px;
}
.footer-section {
    background-color: #1a0f5f;
    color: white;
    padding: 60px 0;
    text-align: center;
}
.footer-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 20px;
}
.footer-subtitle {
    font-size: 1.8rem;
    margin-bottom: 20px;
}
.footer-text {
    font-size: 1rem;
    margin-bottom: 20px;
    line-height: 1.6;
}
.footer-section img {
    width: 100%;
    max-width: 400px;
    height: auto;
    margin: 0 auto;
    display: block;
    border-radius: 15px;
}
.contact-bar {
    background-color: white;
    border-radius: 50px;
    padding: 15px 30px;
    margin-top: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.contact-item {
    color: #333;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}
.contact-item:hover {
    color: #5e50f9;
}

.position-relative {
    z-index: 2;
}

.logo-large {
    font-size: 2.5rem;
}

.img-fluid {
    border-radius: 15px;
    max-width: 100%;
    height: auto;
}
/* Custom styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.header {
    background-color: #1a0f5f;
    padding: 15px 20px;
    color: white;
}
.header img {
    width: 50px;
    height: auto;
    border-radius: 10px;
    margin: 0 auto;
    display: block;
}
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #5e50f9;
}
.btn-get-started {
    background-color: #5e50f9;
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
}
.btn-get-started:hover {
    background-color: #4a3ef7;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.user-icon {
    background-color: white;
    color: #5e50f9;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}
.hero-section {
    position: relative;
}
.hero-image {
    width: 100%;
    height: auto;
    margin: 0 auto;
    display: block;
}
.assessment-header {
    background-color: #1a0f5f;
    color: white;
    padding: 30px 20px;
    text-align: center;
}
.assessment-title {
    font-size: 2.5rem;
    font-weight: 800;
    text-transform: uppercase;
    margin-bottom: 10px;
}
.assessment-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    text-transform: uppercase;
}
.progress-indicator {
    background-color: white;
    color: #333;
    font-weight: 600;
    font-size: 1.2rem;
    padding: 8px 30px;
    border-radius: 50px;
    display: inline-block;
    margin-top: 20px;
}
.career-path-section {
    background-color: #e8f0fe;
    padding: 50px 0;
}
.career-path-section img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 15px;
    margin: 0 auto;
    display: block;
}
.career-path-title {
    font-size: 3rem;
    font-weight: 700;
    color: #0a2540;
    margin-bottom: 20px;
}
.career-path-text {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 30px;
}
.btn-take-assessment {
    background-color: #0a2540;
    color: white;
    border-radius: 5px;
    padding: 12px 25px;
    font-weight: 600;
    border: none;
    font-size: 1.1rem;
}
.question-section {
    background-color: #1a0f5f;
    color: white;
    padding: 50px 20px;
    text-align: center;
}
.question-section img {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 10px;
    margin: 0 auto;
    display: block;
}
.question-text {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 40px;
}
.answer-btn {
    background-color: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 50px;
    padding: 12px 30px;
    font-size: 1.1rem;
    margin: 10px;
    width: 100%;
    transition: all 0.3s;
}
.answer-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
.btn-confirm {
    background-color: white;
    color: #1a0f5f;
    border-radius: 50px;
    padding: 12px 40px;
    font-weight: 600;
    font-size: 1.1rem;
    margin-top: 30px;
    border: none;
}
.contact-bar {
    background-color: white;
    border-radius: 50px;
    padding: 15px 30px;
    margin-top: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.contact-item {
    color: #333;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}
.contact-item:hover {
    color: #5e50f9;
}
/* Custom styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
.header {
    background-color: #1a0f5f;
    padding: 15px 20px;
    color: white;
}
.header img {
    width: 50px;
    height: auto;
    border-radius: 10px;
}
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #5e50f9;
}
.btn-get-started {
    background-color: #5e50f9;
    color: white;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
}
.btn-get-started:hover {
    background-color: #4a3ef7;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}
.user-icon {
    background-color: white;
    color: #5e50f9;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
}
.hero-section {
    position: relative;
}
.hero-image {
    width: 100%;
    height: 50%;
    margin: 0 auto;
    display: block;
}
.recommendations-section {
    background-color: #e8f0fe;
    padding: 40px 0;
}
.recommendations-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0a2540;
    margin-bottom: 20px;
}
.recommendations-text {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 30px;
}
.btn-filters {
    background-color: white;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 8px 20px;
    font-weight: 500;
}
.btn-filters i {
    margin-right: 10px;
}
.school-list {
    background-color: #1a0f5f;
    padding: 20px 0;
}
.school-card {
    background-color: white;
    border-radius: 15px;
    padding: 15px;
    margin: 15px 0;
    display: flex;
    align-items: center;
    position: relative;
}
.school-image {
    width: 100%;
    max-width: 150px;
    height: auto;
    border-radius: 15px;
    margin-right: 20px;
}
.school-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}
.school-type {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    margin-bottom: 10px;
}
.school-score {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    position: absolute;
    left: 150px;
    bottom: 15px;
}
.btn-see {
    background-color: #5e50f9;
    color: white;
    border: none;
    border-radius: 50px;
    padding: 10px 30px;
    font-weight: 600;
    position: absolute;
    right: 20px;
    bottom: 20px;
    transition: background-color 0.3s;
}
.btn-see:hover {
    background-color: #4a3ef7;
}
.arrow-marker {
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    font-size: 1.5rem;
}
.btn-more {
    background-color: white;
    color: #1a0f5f;
    border-radius: 50px;
    padding: 8px 30px;
    font-weight: 600;
    margin: 20px auto;
    display: block;
}
.top-schools-section {
    background-color: #1a0f5f;
    color: white;
    padding: 30px 0;
}
.top-schools-title {
    font-size: 1.8rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
}
.school-ranking-table {
    background-color: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.school-ranking-row {
    display: flex;
    border-bottom: 1px solid #eee;
}
.school-ranking-name {
    flex: 1;
    padding: 15px;
    text-align: right;
    font-weight: 500;
    color: #333;
}
.school-ranking-score {
    width: 40%;
    background-color: #7dd3fc;
    padding: 15px;
    text-align: right;
    font-weight: 700;
    color: #333;
}
.contact-bar {
    background-color: white;
    border-radius: 50px;
    padding: 15px 30px;
    margin-top: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.contact-item {
    color: #333;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}
.contact-item:hover {
    color: #5e50f9;
}
